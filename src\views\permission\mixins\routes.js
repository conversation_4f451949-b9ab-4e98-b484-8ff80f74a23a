// Just a mock data

// 基础路由（不包含登录页面）
const baseConstantRoutes = [
  {
    path: "/redirect",
    hidden: true,
    children: [
      {
        path: "/redirect/:path*",
      },
    ],
  },
  {
    path: "/auth-redirect",
    hidden: true,
  },
  {
    path: "/404",
    hidden: true,
  },
  {
    path: "/401",
    hidden: true,
  },
];

// 登录页面路由
const loginRoute = {
  path: "/login",
  hidden: true,
};

// 根据环境变量决定是否包含登录页面
const constantRoutes = [
  ...baseConstantRoutes,
  // 只有在本地开发模式下才添加登录页面
  ...(process.env.VUE_APP_DEPLOY_TYPE === "local" ? [loginRoute] : [])
];

const asyncRoutes = [
  {
    path: "/",
    children: [
      {
        path: "home",
        meta: { title: "home" },
      },
    ],
  },
  {
    path: "/project/:projectId",
    meta: {
      title: "报告管理",
    },
    children: [
      {
        path: "report",
        meta: {
          title: "报告管理",
        },
      },
      // {
      //   path: "data-overview",
      //   meta: {
      //     title: "报告概览",
      //   },
      // },
      {
        path: "rules",
        meta: {
          title: "规则管理",
        },
      },
      {
        path: "resource-check",
        meta: {
          title: "资源检查",
        },
      },
      {
        path: "branch",
        meta: {
          title: "分支管理",
        },
      },
    ],
  },
  // {
  //   path: "/resourcedetect",
  //   meta: {
  //     title: "resourceDetect",
  //   },
  //   children: [
  //     {
  //       path: "project",
  //       meta: {
  //         title: "myProject",
  //       },
  //     },
  //     {
  //       path: "rulelist",
  //       meta: {
  //         title: "rulesList",
  //       },
  //     },
  //     {
  //       path: "rulecreate",
  //       meta: {
  //         title: "ruleCreate",
  //       },
  //     },
  //   ],
  // },
  // {
  //   path: '/project',
  //   children: [
  //     {
  //       path: 'index',
  //       meta: { title: 'myProject' },
  //     },
  //   ]
  // },
  // {
  //   path: "/prodetail",
  //   meta: { title: "projectDetail" },
  //   children: [
  //     {
  //       path: "report",
  //       meta: { title: "projectReport" },
  //     },
  //     {
  //       path: "reportdetail",
  //       meta: { title: "reportDetail" },
  //     },
  //     {
  //       path: "reportcomparison",
  //       meta: { title: "reportComparison" },
  //     },
  //   ],
  // },
  // {
  //   path: "/ruleedit",
  //   children: [
  //     {
  //       path: "checkrule",
  //       meta: { title: "ruleEdit" },
  //     },
  //   ],
  // },
  // {
  //   path: "/tabledetect",
  //   meta: {
  //     title: "tableDetect",
  //   },
  //   children: [
  //     {
  //       path: "project",
  //       meta: {
  //         title: "myProject",
  //       },
  //     },
  //     {
  //       path: "rulelist",
  //       meta: {
  //         title: "rulesList",
  //       },
  //     },
  //     {
  //       path: "rulereport",
  //       meta: { title: "ruleReport" },
  //     },
  //     {
  //       path: "rulecreate",
  //       meta: {
  //         title: "ruleCreate",
  //       },
  //     },
  //   ],
  // },
  // {
  //   path: "/tabledetail",
  //   meta: { title: "tableDetectDetail" },
  //   children: [
  //     {
  //       path: "report",
  //       meta: { title: "tableProjectReport" },
  //     },
  //     // {
  //     //   path: "checkdetail",
  //     //   meta: { title: "reportDetail" },
  //     // },
  //     // {
  //     //   path: "reportdetail",
  //     //   meta: { title: "reportDetail" },
  //     // },
  //   ],
  // },
  // {
  //   path: "/tableruleedit",
  //   children: [
  //     {
  //       path: "editrule",
  //       meta: { title: "tableRuleEdit" },
  //     },
  //   ],
  // },

  // {
  //   path: '/rulelist',
  //   children: [
  //     {
  //       path: 'index',
  //       meta: { title: 'rulesList' }
  //     }
  //   ]
  // },
  // {
  //   path: '/ruleedit',
  //   children: [
  //     {
  //       path: 'checkrule',
  //       meta: { title: 'ruleEdit' }
  //     }
  //   ]
  // },
  // {
  //   path: '/rulecreate',
  //   children: [
  //     {
  //       path: 'index',
  //       meta: { title: 'ruleCreate' }
  //     }
  //   ]
  // },
  {
    path: "/permission",
    meta: {
      title: "permission",
    },
    children: [
      {
        path: "role",
        meta: {
          title: "roleManagement",
        },
      },
      {
        path: "users",
        meta: {
          title: "userManagement",
        },
      },
      // {
      //   path: "resourceproject",
      //   meta: {
      //     title: "projectManagement",
      //   },
      // },
      // {
      //   path: "tableproject",
      //   meta: {
      //     title: "tableManagement",
      //   },
      // },
    ],
  },
  // {
  //   path: '/helpguide',
  //   meta: {
  //     title: 'helpGuide',
  //   },
  //   children: [
  //     {
  //       path: 'guide1',
  //       meta: {
  //         title: 'helpGuide01'
  //       }
  //     },
  //     {
  //       path: 'guide2',
  //       meta: {
  //         title: 'helpGuide02'
  //       }
  //     },
  //   ]
  // },
];

module.exports = {
  constantRoutes,
  asyncRoutes,
};
