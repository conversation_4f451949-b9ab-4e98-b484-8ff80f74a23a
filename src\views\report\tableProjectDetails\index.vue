<template>
  <div class="table-overview-container">
    <!-- 概览卡片区域 -->
    <div class="overview-cards">
      <!-- 分支详情和关联仓库 - 一行展示 -->
      <div class="overview-row">
        <!-- 分支详情卡片 -->
        <div class="overview-card">
          <div class="card-header">
            <h4 class="card-title">
              <i class="el-icon-s-data"></i>
              分支详情
            </h4>
          </div>
          <div class="card-content">
            <el-descriptions :column="2" size="small">
              <el-descriptions-item :label="$t('projectDetail.projectName')">
                <span class="desc-value">{{ branchStat.project_name || '-' }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="分支名称">
                <span class="desc-value">{{ branchStat.branch || '-' }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="包含规则">
                <el-tag size="mini" type="info">{{ branchStat.branch_rule_count || 0 }} 个</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="启用规则">
                <el-tag size="mini" type="success">{{ branchStat.branch_rule_is_enabled || 0 }} 个</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="错误次数">
                <el-tag size="mini" :type="branchStat.total_effective_count > 0 ? 'danger' : 'success'">
                  {{ branchStat.total_effective_count || 0 }} 次
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="最后检查">
                <span class="desc-value time-value">{{ formatTime(branchStat.scan_time) || '暂无' }}</span>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>

        <!-- 关联仓库卡片 -->
        <div class="overview-card">
          <div class="card-header">
            <h4 class="card-title">
              <i class="el-icon-link"></i>
              关联仓库
            </h4>
            <div class="card-actions" v-loading="updateLoading" element-loading-text="更新中...">
              <el-button
                v-if="!rpeoDetail.url"
                type="text"
                size="mini"
                icon="el-icon-plus"
                @click="openRepoDialog"
                class="action-btn"
              >
                关联仓库
              </el-button>
              <template v-else>
                <el-button
                  type="text"
                  size="mini"
                  icon="el-icon-refresh"
                  @click="updateRpeo"
                  class="action-btn"
                >
                  更新
                </el-button>
                <el-popconfirm title="确定删除吗？" @confirm="deleteRpeo">
                  <el-button
                    type="text"
                    size="mini"
                    icon="el-icon-delete"
                    class="action-btn danger"
                    slot="reference"
                  >
                    删除
                  </el-button>
                </el-popconfirm>
              </template>
            </div>
          </div>
          <div class="card-content">
            <el-descriptions :column="1" size="small">
              <el-descriptions-item label="仓库地址">
                <span class="desc-value repo-url" :title="rpeoDetail.url">
                  {{ rpeoDetail.url || '暂无' }}
                </span>
              </el-descriptions-item>
              <el-descriptions-item label="仓库分支">
                <span class="desc-value">{{ rpeoDetail.repository_branch || '暂无' }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="版本号">
                <span class="desc-value">{{ rpeoDetail.version || '暂无' }}</span>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>
      </div>
    </div>
      <!-- 关联仓库弹窗 -->
      <el-dialog
        title="关联仓库"
        :visible.sync="repoDialogVisible"
        width="30%"
        @close="closeRepoDialog"
        ><div v-loading="repoLoading" element-loading-text="关联仓库中...">
          <el-form
            :model="repoForm"
            :rules="repoRules"
            ref="repoForm"
            label-width="auto"
            label-position="left"
          >
            <el-form-item label="仓库类型" prop="stype">
              <el-select
                v-model="repoForm.stype"
                placeholder="请选择类型"
                style="width: 150px"
              >
                <el-option label="git" value="git" />
                <el-option label="svn" value="svn" />
              </el-select>
            </el-form-item>
            <el-form-item label="仓库地址" prop="url">
              <el-input
                v-model="repoForm.url"
                placeholder="请输入仓库地址"
              ></el-input>
            </el-form-item>
            <el-form-item label="仓库分支" prop="repository_branch">
              <el-input
                v-model="repoForm.repository_branch"
                placeholder="请输入分支名称"
              ></el-input>
            </el-form-item>
            <el-form-item label="用户名" prop="user">
              <el-input
                v-model="repoForm.user"
                placeholder="请填写仓库登录用户名"
              />
            </el-form-item>
            <el-form-item label="用户密码" prop="password">
              <el-input
                v-model="repoForm.password"
                show-password
                placeholder="请填写仓库登录密码"
              />
            </el-form-item>
          </el-form>
        </div>

        <span slot="footer">
          <el-button type="primary" @click="addRpeo">关 联</el-button>
          <el-button @click="closeRepoDialog">取 消</el-button>
        </span>
      </el-dialog>
      <!-- 检查记录表格 -->
      <!-- <div>
        <h4>{{ "检查记录" }}</h4>
        <div style="margin-bottom: 20px">
          <el-input
            v-model="listQuery.keyword"
            style="width: 15%; margin-right: 5px"
            placeholder="请输入关键字模糊搜索"
            clearable
            @clear="searchResult"
          />
          <el-button
            type="primary"
            icon="el-icon-search"
            @click="searchResult()"
            >搜索</el-button
          >
          <el-button type="primary" icon="el-icon-thumb" @click="fullCheckTable"
            >全量检查</el-button
          >
        </div>
        <el-table :data="checkRecordData" border style="width: 100%">
          <el-table-column prop="check_time" label="检查时间" />
          <el-table-column prop="check_type" label="检查方式" />
          <el-table-column prop="user" label="触发人" />
          <el-table-column prop="status" label="当前状态" />
          <el-table-column prop="result" label="检查结果" />
          <el-table-column
            label="操作"
            fixed="right"
            width="120"
            align="center"
          >
            <template slot-scope="scope">
              <el-link
                type="primary"
                :underline="false"
                @click="viewRecord(scope.row)"
                style="margin-right: 15px"
                >查看</el-link
              >
              <el-popconfirm
                title="确定删除吗？"
                @confirm="deleteRecord(scope.row)"
              >
                <el-link type="danger" :underline="false" slot="reference"
                  >删除</el-link
                >
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </div> -->

    <!-- 检查结果区域 -->
    <!-- <div class="results-section">
      <div class="section-header">
        <h4 class="section-title">
          <i class="el-icon-document-checked"></i>
          检查结果
        </h4>
        <div class="section-actions">
          <el-input
            v-model="listQuery.keyword"
            placeholder="搜索关键字..."
            size="small"
            clearable
            @clear="searchResult"
            class="search-input"
          >
            <el-button
              slot="append"
              icon="el-icon-search"
              @click="searchResult()"
            ></el-button>
          </el-input>
          <el-button
            type="primary"
            size="small"
            icon="el-icon-refresh"
            @click="fullCheckTable"
            class="check-btn"
          >
            全量检查
          </el-button>
        </div>
      </div>

      <div class="table-container">
        <el-table
          :data="tableData"
          style="width: 100%"
          size="small"
          :height="tableHeight"
          stripe
          highlight-current-row
          border
        >
          <el-table-column
            prop="trigger_time"
            label="触发时间"
            width="160"
            show-overflow-tooltip
            align="center"
          >
            <template slot-scope="scope">
              <span class="time-text">{{ formatTime(scope.row.trigger_time) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="rule_name"
            label="触发规则"
            show-overflow-tooltip
            min-width="150"
            align="left"
          />
          <el-table-column
            prop="trigger_method"
            label="触发形式"
            width="100"
            align="center"
          />
          <el-table-column
            prop="user"
            label="触发人"
            width="100"
            align="center"
          />
          <el-table-column
            prop="branch"
            label="分支"
            width="120"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="status"
            label="检查结果"
            width="100"
            align="center"
          >
            <template slot-scope="scope">
              <el-tag
                size="mini"
                :type="getStatusTagType(scope.row.status)"
              >
                {{ scope.row.status || '未知' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="错误内容"
            show-overflow-tooltip
            min-width="200"
            align="left"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.check_result" class="error-content">
                {{ scope.row.check_result }}
              </span>
              <span v-else class="no-error">无错误</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            width="80"
            align="center"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button
                type="text"
                size="mini"
                icon="el-icon-view"
                @click="checkMore(scope.row)"
                class="view-btn"
              >
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="pagination-wrapper">
        <Pagination
          :total="total"
          :page.sync="listQuery.page"
          :limit.sync="listQuery.limit"
          :auto-scroll="listQuery.limit >= 30"
          @pagination="pageSwitch"
        />
      </div>
    </div> -->

    <!-- 关联仓库弹窗 -->
    <el-dialog
      title="关联仓库"
      :visible.sync="repoDialogVisible"
      width="500px"
      @close="closeRepoDialog"
      class="repo-dialog"
    >
      <div v-loading="repoLoading" element-loading-text="关联仓库中...">
        <el-form
          :model="repoForm"
          :rules="repoRules"
          ref="repoForm"
          label-width="100px"
          label-position="left"
        >
          <el-form-item label="仓库类型" prop="stype">
            <el-select
              v-model="repoForm.stype"
              placeholder="请选择类型"
              style="width: 100%"
            >
              <el-option label="Git" value="git" />
              <el-option label="SVN" value="svn" />
            </el-select>
          </el-form-item>
          <el-form-item label="仓库地址" prop="url">
            <el-input
              v-model="repoForm.url"
              placeholder="请输入仓库地址"
            ></el-input>
          </el-form-item>
          <el-form-item label="仓库分支" prop="repository_branch">
            <el-input
              v-model="repoForm.repository_branch"
              placeholder="请输入分支名称"
            ></el-input>
          </el-form-item>
          <el-form-item label="用户名" prop="user">
            <el-input
              v-model="repoForm.user"
              placeholder="请填写仓库登录用户名"
            />
          </el-form-item>
          <el-form-item label="用户密码" prop="password">
            <el-input
              v-model="repoForm.password"
              show-password
              placeholder="请填写仓库登录密码"
            />
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer">
        <el-button @click="closeRepoDialog">取 消</el-button>
        <el-button type="primary" @click="addRpeo">关 联</el-button>
      </span>
    </el-dialog>

    <!-- 查看更多弹窗 -->
    <el-dialog
      title="检查结果详情"
      :visible.sync="dialogVisible"
      width="600px"
      class="detail-dialog"
    >
      <div class="detail-content">
        <el-descriptions direction="vertical" :column="1" border>
          <el-descriptions-item v-if="errorInfo.check_result" label="检查结果">
            <pre class="result-content">{{ errorInfo.check_result }}</pre>
          </el-descriptions-item>
        </el-descriptions>
        <div v-if="!errorInfo.check_result" class="no-info">
          <el-empty description="暂无详细信息"></el-empty>
        </div>
      </div>
      <span slot="footer">
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>
  </div>

</template>

<script>
import BackToTop from "@/components/BackToTop";
import {
  getTableBranchDetail,
  fullScanExcel,
  getTableCheckResult,
  addTableRpeo,
  updateTableRpeo,
  delTableRpeo,
} from "@/api/project";
import Pagination from "@/components/Pagination/index.vue";
import { mapGetters, mapActions } from "vuex";

export default {
  name: "Prodetail",
  components: { Pagination, BackToTop },
  data() {
    return {
      branchStat: {}, // 项目分支详情
      checkRecordData: [], // 检查记录列表
      tableData: [],
      total: 0,
      listQuery: {
        keyword: "",
        page: 1,
        limit: 20,
      },
      dialogVisible: false,
      errorInfo: { exception_result: "", error_report: {} },

      rpeoDetail: {}, //保存关联仓库详情
      repoDialogVisible: false,
      repoLoading: false,
      updateLoading: false,
      repoForm: {
        stype: "git",
        repository_branch: "",
        url: "",
        user: "",
        password: "",
      },
      repoRules: {
        repository_branch: [
          { required: true, message: "请输入分支名称", trigger: "blur" },
        ],
        url: [{ required: true, message: "请输入仓库地址", trigger: "blur" }],
        user: [{ required: true, message: "请输入用户名", trigger: "blur" }],
        password: [
          { required: true, message: "请输入用户密码", trigger: "blur" },
        ],
      },

      // 表格高度
      tableHeight: 400,
    };
  },
  // 在页面离开时记录滚动位置
  beforeRouteLeave(to, from, next) {
    this.scrollTop =
      document.documentElement.scrollTop || document.body.scrollTop;
    next();
  },
  // 进入该页面时，用之前保存的滚动位置赋值
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      document.body.scrollTop = vm.scrollTop;
    });
  },

  mounted() {
    this.initializeData();
    this.calculateTableHeight();
    window.addEventListener('resize', this.calculateTableHeight);
  },

  beforeDestroy() {
    window.removeEventListener('resize', this.calculateTableHeight);
  },
  watch: {
    // 监听路由变化，更新项目ID
    '$route'(to, from) {
      if (to.params.projectId !== from.params.projectId) {
        console.log('项目ID变化，重新获取数据:', to.params.projectId);
        this.initializeData();
      }
    },
    // 监听选中分支变化
    selectedBranch(newBranch, oldBranch) {
      if (newBranch !== oldBranch && newBranch && this.currentProjectId) {
        console.log('分支变化，重新获取数据:', newBranch);
        this.refreshData();
      }
    },
    // 监听选中检查类型变化
    selectedCheckType(newType, oldType) {
      if (newType !== oldType && newType && this.currentProjectId && this.selectedBranch) {
        console.log('检查类型变化，重新获取数据:', newType);
        this.refreshData();
      }
    }
  },
  computed: {
    ...mapGetters(["projectName", "projectBranch", "selectedBranch", "selectedCheckType"]),
    // 获取当前项目ID
    currentProjectId() {
      return this.$route.params.projectId || sessionStorage.getItem('currentProjectId')
    }
  },
  methods: {
    ...mapActions('project', ['fetchBranchOptions']),

    // 初始化数据
    async initializeData() {
      if (!this.currentProjectId) {
        console.warn('未找到项目ID');
        return;
      }
      // 获取报告数据
      this.refreshData();
    },

    // 刷新数据
    refreshData() {
      this.getBranchDetail();
      // this.getCheckRecordList();
      this.geCheckResultList();
    },

    // 获取分支详情数据
    async getBranchDetail() {
      const projectId = this.currentProjectId;
      const branch = this.selectedBranch || this.$route.query.branch;

      if (!projectId || !branch) {
        console.warn('缺少项目ID或分支信息:', { projectId, branch });
        return;
      }

      const params = {
        project_id: projectId,
        branch: branch,
      };
      const { code, data } = await getTableBranchDetail(params);
      if (code === 200) {
        this.branchStat = data;
        this.rpeoDetail = data.repository;
      }
    },

    // 全量检查表格
    async fullCheckTable() {
      const projectId = this.currentProjectId;
      const branch = this.selectedBranch || this.$route.query.branch;

      if (!projectId || !branch) {
        console.warn('缺少项目ID或分支信息:', { projectId, branch });
        return;
      }

      const params = {
        project_id: projectId,
        branch: branch,
      };
      const { code, msg } = await fullScanExcel(params);
      if (code === 200) {
        this.$message.success(msg);
        this.refreshData()
      }
    },

    // 获取检查记录列表数据
    async getCheckRecordList() {
      // mockdata
      const data = [
        {
          id: 1,
          check_time: "2024-08-03 12:00:00",
          check_type: "全量检查",
          user: "用户1",
          status: "检查中",
          result: "通过",
        },
        {
          id: 2,
          check_time: "2024-08-03 12:00:00",
          check_type: "全量检查",
          user: "用户1",
          status: "已完成",
          result: "未通过",
        },
      ];
      // const params = {
      //   project_id: this.$route.query.projectId,
      //   branch,
      //   search: this.listQuery.keyword,
      //   pageNum: this.listQuery.page,
      //   pageSize: this.listQuery.limit,
      // };
      // const { code, msg, data, total_count } = await getTableCheckResult(
      //   params
      // );
      // if (code !== 200) return this.$message.warning(msg);
      this.checkRecordData = data;
      this.total = data.length;
    },
    // 查看检查记录
    async viewRecord(row) {
      console.log(row);
      const projectId = this.currentProjectId;
      const branch = this.selectedBranch || this.$route.query.branch;

      const params = {
        project_id: projectId,
        branch: branch,
        check_id: row.id,
      };
      this.$router.push({
        path: "/tabledetail/checkdetail",
        query: params,
      });
    },
    deleteRecord(row) {
      console.log(row);
    },

    // 获取检查结果列表
    async geCheckResultList() {
      const projectId = this.currentProjectId;
      const branch = this.selectedBranch || this.$route.query.branch;

      if (!projectId || !branch) {
        console.warn('缺少项目ID或分支信息:', { projectId, branch });
        this.goBack();
        return;
      }

      // mock data
      // const data = [
      //   {
      //     time: "2024-07-03 12:00:00",
      //     rule: "规则1",
      //     type: "类型1",
      //     user: "用户1",
      //     branch: "分支1",
      //     error:
      //       "错误内容中只显示信息的部分内容后续使用省略号代替，点击查看更多时再弹出二级界面显示所有的详细的错误信息",
      //   },
      //   {
      //     time: "2024-07-03 12:00:00",
      //     rule: "规则2",
      //     type: "类型2",
      //     user: "用户2",
      //     branch: "分支2",
      //     error: "错误2",
      //   },
      // ];
      // this.tableData = data;
      // this.total = data.length;
      const params = {
        project_id: projectId,
        branch: branch,
        search: this.listQuery.keyword,
        pageNum: this.listQuery.page,
        pageSize: this.listQuery.limit,
      };
      const { code, msg, data, total_count } = await getTableCheckResult(
        params
      );
      if (code !== 200) return this.$message.warning("检查结果" + msg);
      this.tableData = data;
      this.total = total_count ? total_count : 0;
    },

    // 分页器页码切换 获取页码和页数
    pageSwitch(val) {
      this.geCheckResultList();
    },
    searchResult() {
      this.listQuery.page = 1;
      this.listQuery.limit = 20;
      // console.log("搜索", this.listQuery);
      this.geCheckResultList();
    },
    // 查看更多详情页
    checkMore(row) {
      // console.log(row);
      this.errorInfo = row;
      this.dialogVisible = true;
    },

    // 打开关联仓库弹窗
    openRepoDialog() {
      this.repoForm.branch = this.selectedBranch || this.$route.query.branch;
      this.repoDialogVisible = true;
    },
    // 关闭关联仓库弹窗
    closeRepoDialog() {
      this.$refs.repoForm.resetFields();
      this.repoDialogVisible = false;
    },
    // 获取关联仓库详情
    async getRpeoDetail() {
      const projectId = this.currentProjectId;
      const branch = this.selectedBranch || this.$route.query.branch;

      const params = {
        project_id: projectId,
        branch: branch,
      };
      // const { code, data } = await getRpeoDetail(params);
      // if (code === 200) {
      //   this.rpeoDetail = data;
      // }
      const data = {
        // url: "xxxxxxxxxxxxxxxxxxxxxxxxxx",
        // branch: "master",
        // version: "1.0.0",
      };
      this.rpeoDetail = data;
    },
    // 关联仓库
    addRpeo() {
      this.$refs.repoForm.validate((valid) => {
        if (valid) {
          const projectId = this.currentProjectId;
          const branch = this.selectedBranch || this.$route.query.branch;

          const params = {
            project_id: projectId,
            branch: branch,
            ...this.repoForm,
          };
          // const { code, msg } = addTableGit(params);
          this.repoLoading = true;
          addTableRpeo(params).then(({ code, msg }) => {
            if (code === 200) {
              this.$message.success(msg);
              this.repoLoading = false;
              this.getBranchDetail();
            }
            this.closeRepoDialog();
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    // 更新仓库
    async updateRpeo() {
      const projectId = this.currentProjectId;
      const branch = this.selectedBranch || this.$route.query.branch;

      const params = {
        project_id: projectId,
        branch: branch,
        stype: this.repoForm.stype,
      };
      this.updateLoading = true;
      const { code, msg } = await updateTableRpeo(params);
      if (msg) this.updateLoading = false;
      if (code !== 200) return this.$message.warning(msg);
      this.$message.success(msg);
      this.getBranchDetail();
    },
    // 删除仓库
    async deleteRpeo() {
      const projectId = this.currentProjectId;
      const branch = this.selectedBranch || this.$route.query.branch;

      const params = {
        project_id: projectId,
        branch: branch,
      };
      const { code, msg } = await delTableRpeo(params);
      if (code === 200) {
        this.$message.success(msg);
        this.getBranchDetail();
      }
    },

    goBack() {
      // this.$router.push("/tabledetect/project");
    },

    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return '';
      const date = new Date(timeStr);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
      });
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      if (!status) return 'info';
      const statusLower = status.toLowerCase();
      if (statusLower.includes('通过') || statusLower.includes('成功')) {
        return 'success';
      } else if (statusLower.includes('未通过') || statusLower.includes('失败') || statusLower.includes('错误')) {
        return 'danger';
      } else if (statusLower.includes('警告')) {
        return 'warning';
      } else {
        return 'info';
      }
    },

    // 计算表格高度
    calculateTableHeight() {
      const windowHeight = window.innerHeight;
      const headerHeight = 200; // 概览卡片区域高度
      const sectionHeaderHeight = 60; // 检查结果标题区域高度
      const paginationHeight = 60; // 分页高度
      const padding = 40; // 内边距

      this.tableHeight = windowHeight - headerHeight - sectionHeaderHeight - paginationHeight - padding;
      if (this.tableHeight < 300) {
        this.tableHeight = 300; // 最小高度
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.table-overview-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
  overflow: hidden;
}

// 概览卡片区域
.overview-cards {
  padding: 20px;
  flex-shrink: 0;
}

.overview-row {
  display: flex;
  gap: 20px;
  width: 100%;
}

.overview-card {
  flex: 1;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e8e8e8;
  overflow: hidden;
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #e8e8e8;
}

.card-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;

  i {
    color: #409eff;
    font-size: 18px;
  }
}

.card-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 4px;

  &.danger {
    color: #f56c6c;

    &:hover {
      color: #f56c6c;
      background-color: rgba(245, 108, 108, 0.1);
    }
  }
}

.card-content {
  padding: 20px;
}

.desc-value {
  color: #606266;
  font-weight: 500;

  &.time-value {
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 13px;
  }

  &.repo-url {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
  }
}

// 检查结果区域
.results-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin: 0 20px 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e8e8e8;
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #e8e8e8;
  flex-shrink: 0;
}

.section-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;

  i {
    color: #409eff;
    font-size: 18px;
  }
}

.section-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-input {
  width: 250px;
}

.check-btn {
  white-space: nowrap;
}

.table-container {
  flex: 1;
  overflow: hidden;

  ::v-deep .el-table {
    .el-table__header-wrapper {
      .el-table__header {
        th {
          background-color: #f8f9fa;
          color: #606266;
          font-weight: 500;
          font-size: 13px;
          border-bottom: 1px solid #e8e8e8;
          padding: 12px 0;

          .cell {
            padding: 0 10px;
            line-height: 1.4;
          }
        }
      }
    }

    .el-table__body-wrapper {
      .el-table__body {
        tr {
          &:hover {
            background-color: #f5f7fa;
          }

          &.current-row {
            background-color: #ecf5ff;
          }

          td {
            font-size: 13px;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
            vertical-align: middle;

            .cell {
              padding: 0 10px;
              line-height: 1.4;
              word-break: break-word;
            }
          }
        }
      }
    }

    // 修复固定列的对齐问题
    .el-table__fixed-right {
      .el-table__fixed-body-wrapper {
        .el-table__body {
          tr {
            td {
              vertical-align: middle;

              .cell {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 100%;
              }
            }
          }
        }
      }

      .el-table__fixed-header-wrapper {
        .el-table__header {
          th {
            .cell {
              display: flex;
              align-items: center;
              justify-content: center;
              height: 100%;
            }
          }
        }
      }
    }
  }
}

.time-text {
  color: #909399;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 12px;
}

.error-content {
  color: #606266;
  line-height: 1.4;
}

.no-error {
  color: #909399;
  font-style: italic;
}

.view-btn {
  color: #409eff;

  &:hover {
    color: #66b1ff;
  }
}

.pagination-wrapper {
  padding: 16px 20px;
  border-top: 1px solid #e8e8e8;
  background-color: #fafafa;
  flex-shrink: 0;
  display: flex;
  justify-content: center;
}

// 弹窗样式
.repo-dialog {
  ::v-deep .el-dialog__body {
    padding: 20px;
  }
}

.detail-dialog {
  ::v-deep .el-dialog__body {
    padding: 20px;
  }
}

.detail-content {
  max-height: 400px;
  overflow-y: auto;
}

.result-content {
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 4px;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 13px;
  line-height: 1.6;
  color: #333;
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 0;
}

.no-info {
  text-align: center;
  padding: 20px;
}

// 响应式设计
@media (max-width: 1200px) {
  .overview-row {
    flex-direction: column;
    gap: 15px;
  }

  .section-actions {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .search-input {
    width: 200px;
  }
}

@media (max-width: 768px) {
  .table-overview-container {
    background-color: #fff;
  }

  .overview-cards {
    padding: 15px;
  }

  .overview-row {
    flex-direction: column;
    gap: 15px;
  }

  .results-section {
    margin: 0 15px 15px;
  }

  .section-header {
    padding: 12px 15px;
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .search-input {
    width: 100%;
  }

  // 移动端表格优化
  .table-container {
    ::v-deep .el-table {
      .el-table__header-wrapper,
      .el-table__body-wrapper {
        .cell {
          padding: 0 5px;
          font-size: 12px;
        }
      }
    }
  }
}

// 深度选择器兼容性
::v-deep .el-descriptions-item__content {
  white-space: pre-wrap;
}

::v-deep .pagination-container {
  background: transparent;
  padding: 0;
  margin: 0;
}
</style>
