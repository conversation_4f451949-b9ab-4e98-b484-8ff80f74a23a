<template>
  <div class="app-container">
    <div class="reports-layout">
      <!-- 左侧报告列表 35% -->
      <div class="reports-left">
        <div class="reports-header">
          <div class="reports-title">
            <h3>
              报告列表({{ selectedCheckType === 1 ? "美术资源" : "表格资源" }})
            </h3>
            <span class="reports-count">共 {{ total }} 条报告</span>
          </div>
          <!-- <div class="reports-actions">
            <el-button
              type="text"
              size="mini"
              icon="el-icon-refresh"
              @click="handleRefresh"
            >
              刷新
            </el-button>
          </div> -->
        </div>

        <!-- 筛选区域 -->
        <!-- <div class="reports-filters">
          <el-select
            v-model="filterType"
            placeholder="触发类型"
            size="small"
            style="width: 120px; margin-right: 10px"
            @change="handleFilterChange"
          >
            <el-option label="全部" value="all"></el-option>
            <el-option label="手动" value="manual"></el-option>
            <el-option label="自动" value="auto"></el-option>
          </el-select>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            size="small"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
            @change="handleDateChange"
          />
        </div> -->

        <!-- 报告列表表格 -->
        <div class="reports-table">
          <el-table
            :data="reportsList"
            style="width: 100%"
            size="small"
            :height="tableHeight"
            @row-click="handleRowClick"
            highlight-current-row
          >
            <el-table-column prop="id" label="ID" align="center" width="80">
              <template slot-scope="scope">
                <span>{{ scope.row.id || "-" }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="scan_time"
              label="检查时间"
              show-overflow-tooltip
              width="180"
            >
              <template slot-scope="scope">
                <span>{{ formatTime(scope.row.scan_time) }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="tigger_type"
              label="触发类型"
              show-overflow-tooltip
            >
            </el-table-column>
            <el-table-column
              prop="errorTotal"
              label="检查结果"
              align="center"
              width="100"
            >
              <template slot-scope="scope">
                <el-tag
                  :type="scope.row.passTotal!==scope.row.count ? 'danger' : 'success'"
                  size="mini"
                >
                  {{ scope.row.passTotal+'/'+scope.row.count }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
              <template slot-scope="scope">
                <el-tooltip
                  content="查看详情"
                  placement="top"
                  :open-delay="500"
                >
                  <el-button
                    type="text"
                    size="mini"
                    icon="el-icon-view"
                    class="action-btn"
                    @click.stop="handleViewDetail(scope.row)"
                  />
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分页 -->
        <div class="reports-pagination">
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="listQuery.page"
            :limit.sync="listQuery.limit"
            @pagination="handlePageChange"
          />
        </div>
      </div>

      <!-- 右侧报告详情 65% -->
      <div class="reports-right">
        <!-- 分支报告概览 -->
        <div v-if="rightPanelType === 'overview'" class="overview-content">
          <div class="overview-header">
            <h3 class="overview-title">
              分支报告概览({{ selectedCheckType === 1 ? "美术资源" : "表格资源" }})
            </h3>
            <div class="overview-info">
              <span class="branch-info">分支: {{ selectedBranch }}</span>
            </div>
          </div>
          <div class="overview-body">
            <!-- 美术资源概览 -->
            <div v-if="selectedCheckType === 1" class="art-overview">
              <ArtOverviewComponent/>
            </div>
            <!-- 表格资源概览 -->
            <div v-else-if="selectedCheckType === 2" class="table-overview">
              <!-- <TableBranchOverview
                :project-id="projectId"
                :branch="selectedBranch"
                :reports-list="reportsList"
                :total="total"
              /> -->
              <TableOverviewComponent/>
            </div>
          </div>
        </div>

        <!-- 报告详情 -->
        <div v-else-if="rightPanelType === 'detail'" class="detail-content">
          <div class="detail-header">
            <h3 class="detail-title">报告详情 - ID: {{ currentReport.id }}</h3>
            <el-button
              type="text"
              icon="el-icon-close"
              class="close-btn"
              @click="closeRightPanel"
            >
              关闭
            </el-button>
          </div>
          <div class="detail-body">
            <!-- 根据检查类型显示不同的详情组件 -->
            <div v-if="selectedCheckType === 1" class="art-report-detail">
              <!-- 美术资源报告详情 -->
              <ArtReportDetails
                :report-data="currentReport"
                :project-id="projectId"
                :embedded="true"
              />
            </div>
            <div
              v-else-if="selectedCheckType === 2"
              class="table-report-detail"
            >
              <!-- 表格资源报告详情 -->
              <div class="detail-content-body">
                <!-- 基本信息 -->
                <div class="detail-section">
                  <h4 class="section-title">基本信息</h4>
                  <el-descriptions :column="2" border>
                    <el-descriptions-item label="规则名称">
                      {{ currentReport.rule_name || 'N/A' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="检查时间">
                      {{ formatTime(currentReport.scan_time) }}
                    </el-descriptions-item>
                    <el-descriptions-item label="触发方式">
                      {{ currentReport.type || 'N/A' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="分支">
                      {{ currentReport.branch || 'N/A' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="触发用户">
                      {{ currentReport.user || 'N/A' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="检查状态">
                      <el-tag :type="getStatusType(currentReport.status)">
                        {{ currentReport.status || 'N/A' }}
                      </el-tag>
                    </el-descriptions-item>
                  </el-descriptions>
                </div>

                <!-- 检查结果详情 -->
                <div class="detail-section" v-if="currentReport.check_result">
                  <h4 class="section-title">检查结果详情</h4>
                  <div class="check-result-content">
                    <pre class="result-text">{{ currentReport.check_result }}</pre>
                  </div>
                </div>
              </div>
            </div>
            <!-- 未知类型 -->
            <!-- <div v-else class="unknown-type-detail">
              <div class="detail-placeholder">
                <el-alert
                  title="未知的检查类型"
                  type="warning"
                  :closable="false"
                  show-icon
                >
                  <template slot="default">
                    <p>检查类型：{{ selectedCheckType }}</p>
                    <p>请选择正确的检查类型</p>
                  </template>
                </el-alert>
              </div>
            </div> -->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Pagination from "@/components/Pagination";
import { getReportSummary, getTableCheckResult,getTableList } from "@/api/project";
import { mapGetters } from "vuex";
import ArtReportDetails from "./projectreport/detail.vue";
import TableBranchOverview from "./components/TableBranchOverview.vue";
import ArtOverviewComponent from './artOverviewComponent.vue';
import TableOverviewComponent from './tableProjectDetails/index.vue'
export default {
  name: "ReportIndex",
  components: {
    Pagination,
    ArtReportDetails,
    TableBranchOverview,
    ArtOverviewComponent,
    TableOverviewComponent
  },
  data() {
    return {
      projectId: "",
      reportsList: [],
      total: 0,
      listQuery: {
        page: 1,
        limit: 20,
      },
      filterType: "all", // 筛选类型，默认为全部
      dateRange: null, // 日期范围
      tableHeight: 400,
      // 右侧面板状态管理
      rightPanelType: "overview", // 'overview' | 'detail'
      currentReport: null, // 当前查看的报告数据
    };
  },
  computed: {
    ...mapGetters("project", ["selectedBranch", "selectedCheckType"]),
  },
  created() {
    // 从路由参数中获取projectId
    this.projectId = this.$route.params.projectId;
    this.calculateTableHeight();
  },
  mounted() {
    // 监听窗口大小变化
    window.addEventListener("resize", this.calculateTableHeight);
    // 初始化数据获取
    this.initializeData();
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.calculateTableHeight);
  },
  watch: {
    $route(to, from) {
      // 监听路由变化，更新projectId
      if (to.params.projectId !== from.params.projectId) {
        this.projectId = to.params.projectId;
        this.rightPanelType = "overview";
        this.initializeData();
      }
    },
    // 监听选中分支变化
    selectedBranch(newBranch, oldBranch) {
      if (newBranch !== oldBranch && newBranch) {
        this.rightPanelType = "overview";
        this.listQuery.page = 1; // 重置到第一页
        this.fetchReportsList();
      }
    },
    // 监听选中检查类型变化
    selectedCheckType(newType, oldType) {
      if (newType !== oldType && newType) {
        this.rightPanelType = "overview";
        this.listQuery.page = 1; // 重置到第一页
        this.fetchReportsList();
      }
    },
  },
  methods: {
    // 初始化数据
    initializeData() {
      // 等待分支和检查类型数据加载完成后再获取报告列表
      this.$nextTick(() => {
        if (this.selectedBranch && this.selectedCheckType) {
          this.fetchReportsList();
        }
      });
    },

    // 获取报告列表
    async fetchReportsList() {
      try {
        console.log("获取项目报告列表，项目ID:", this.projectId);

        // 检查必要参数
        if (!this.projectId) {
          console.warn("缺少项目ID");
          return;
        }

        // 使用 Vuex 中的分支数据，如果没有则使用 sessionStorage 作为备用
        const branch =
          this.selectedBranch ||
          sessionStorage.getItem("selectedBranch_" + this.projectId);

        if (!branch) {
          console.warn("缺少分支信息");
          return;
        }

        let res;
        // 根据当前选择的检查类型决定使用哪个接口
        if (this.selectedCheckType === 2) {
          // 表格资源 - 使用 getTableCheckResult 接口
          res = await getTableList({
            project_id: this.projectId,
            branch: branch,
            pageNum: this.listQuery.page,
            pageSize: this.listQuery.limit,
          });
        } else {
          // 美术资源 - 使用 getReportSummary 接口
          res = await getReportSummary({
            projectId: this.projectId,
            branch: branch,
            pageNum: this.listQuery.page,
            pageSize: this.listQuery.limit,
          });
        }

        if (res && res.code === 200) {
          // 根据接口类型处理不同的数据结构
          if (this.selectedCheckType === 2) {
            // 表格资源接口返回的数据结构 - 需要转换格式
            const transformedData = this.transformTableData(res.data || []);
            this.reportsList = transformedData;
            this.total = res.total_count || 0;
          } else {
            // 美术资源接口返回的数据结构
            this.reportsList = res.data || [];
            this.total = res.dataLen || 0;
          }
        } else {
          this.$message.error((res && res.msg) || "获取报告列表失败");
          this.reportsList = [];
          this.total = 0;
        }
      } catch (error) {
        console.error("获取报告列表失败:", error);
        this.$message.error("获取报告列表失败");
        this.reportsList = [];
        this.total = 0;
      }
    },

    // 转换表格数据格式，使其与美术资源格式一致
    transformTableData(tableData) {
      return tableData.map((item) => {
        // 生成一个唯一的ID（如果原数据没有ID）
        const id = item.id || '-';

        return {
          // 统一字段格式（与美术资源保持一致）
          id: id,
          scan_time: item.scan_time, // 将trigger_time映射为scan_time
          type: item.trigger_method, // 将trigger_method映射为type
          // 保留表格资源的原始字段
          // rule_name: item.rule_name,
          // branch: item.branch,
          // user: item.user,
          // status: item.status,
          // trigger_method: item.trigger_method,
          // trigger_time: item.trigger_time,
          // check_result: item.check_result,
          tigger_type: item.tigger_type || trigger_method,
          // 添加一些兼容字段
          errorTotal: this.calculateErrorCount(item.check_result),
          count:item.count,
          passTotal:item.passTotal,
          // 保存原始数据，用于详情显示
          // _originalData: { ...item }
        };
      });
    },

    // 根据检查结果计算错误数量
    calculateErrorCount(checkResult) {
      if (!checkResult) return 0;

      // 如果状态包含"错误"相关关键词，则认为有错误
      if (checkResult.includes('错误') || checkResult.includes('Error') || checkResult.includes('异常')) {
        return 1;
      }

      return 0;
    },

    // 根据状态获取标签类型
    getStatusType(status) {
      if (!status) return '';

      const statusLower = status.toLowerCase();
      if (statusLower.includes('错误') || statusLower.includes('error') || statusLower.includes('失败')) {
        return 'danger';
      } else if (statusLower.includes('成功') || statusLower.includes('通过') || statusLower.includes('完成')) {
        return 'success';
      } else if (statusLower.includes('警告') || statusLower.includes('warning')) {
        return 'warning';
      } else {
        return 'info';
      }
    },

    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return "";
      const date = new Date(timeStr);
      return date.toLocaleString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
      });
    },

    // 计算表格高度
    calculateTableHeight() {
      // 根据窗口高度动态计算表格高度
      const windowHeight = window.innerHeight;
      const headerHeight = 120; // 头部高度
      const filtersHeight = 60; // 筛选区域高度
      const paginationHeight = 60; // 分页高度
      const padding = 40; // 内边距

      this.tableHeight =
        windowHeight -
        headerHeight -
        filtersHeight -
        paginationHeight -
        padding;
      if (this.tableHeight < 300) {
        this.tableHeight = 300; // 最小高度
      }
    },

    // 刷新数据
    handleRefresh() {
      this.fetchReportsList();
      this.$message.success("数据已刷新");
    },

    // 筛选类型变化
    handleFilterChange() {
      this.listQuery.page = 1; // 重置到第一页
      this.fetchReportsList();
    },

    // 日期范围变化
    handleDateChange() {
      this.listQuery.page = 1; // 重置到第一页
      this.fetchReportsList();
    },

    // 行点击事件
    handleRowClick(row) {
      this.currentReport = row;
      this.rightPanelType = "detail";
    },

    // 查看详情
    handleViewDetail(row) {
      this.currentReport = row;
      this.rightPanelType = "detail";
    },

    // 关闭右侧面板
    closeRightPanel() {
      this.rightPanelType = "overview";
      this.currentReport = null;
    },

    // 分页变化
    handlePageChange(pagination) {
      this.listQuery.page = pagination.page;
      this.listQuery.limit = pagination.limit;
      this.fetchReportsList();
    },

    // 分支变化处理
    handleBranchChange(branch) {
      console.log("分支变化:", branch);
      this.listQuery.page = 1; // 重置到第一页
      this.fetchReportsList();
    },

    // 检查类型变化处理
    handleCheckTypeChange(checkType) {
      console.log("检查类型变化:", checkType);
      this.listQuery.page = 1; // 重置到第一页
      this.fetchReportsList();
    },
  },
};
</script>

<style scoped lang="scss">
.app-container {
  padding: 20px;
  height: calc(100vh - 50px);
  overflow: hidden;
}

.selector-container {
  margin-bottom: 20px;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  padding: 16px 20px;
}

.reports-layout {
  display: flex;
  gap: 20px;
  height: 100%;
}

.reports-left {
  width: 30%;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.reports-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e8e8e8;
  background-color: #fafafa;
  flex-shrink: 0;
}

.reports-title {
  display: flex;
  align-items: center;
  gap: 12px;

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }
}

.reports-count {
  font-size: 12px;
  color: #999;
  background-color: #f0f0f0;
  padding: 2px 8px;
  border-radius: 10px;
}

.reports-actions {
  display: flex;
  gap: 8px;

  .el-button {
    padding: 4px 8px;
    font-size: 12px;
  }
}

.reports-filters {
  padding: 12px 20px;
  border-bottom: 1px solid #e8e8e8;
  background-color: #fafafa;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.reports-table {
  flex: 1;
  overflow: hidden;
  padding: 0;

  ::v-deep .el-table {
    .el-table__header-wrapper {
      .el-table__header {
        th {
          background-color: #f8f9fa;
          color: #606266;
          font-weight: 500;
          font-size: 13px;
        }
      }
    }

    .el-table__body-wrapper {
      .el-table__body {
        tr {
          cursor: pointer;

          &:hover {
            background-color: #f5f7fa;
          }

          &.current-row {
            background-color: #ecf5ff;
          }

          td {
            font-size: 13px;
            padding: 8px 0;
          }
        }
      }
    }
  }
}

.action-btn {
  color: #606266;

  &:hover {
    color: #409eff;
  }
}

.reports-pagination {
  //padding: 12px 20px;
  border-top: 1px solid #e8e8e8;
  background-color: #fafafa;
  flex-shrink: 0;
  display: flex;
  justify-content: center;
}
::v-deep .reports-pagination {
  .pagination-container {
    background: transparent;
    padding: 10px;
    display: flex;
    justify-content: center;
    margin-top: 0;
  }
}
.reports-right {
  width: 70%;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

// 概览内容样式
.overview-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e8e8e8;
  background-color: #fafafa;
  flex-shrink: 0;
}

.overview-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.overview-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.branch-info {
  font-size: 13px;
  color: #666;
  background-color: #f0f0f0;
  padding: 4px 8px;
  border-radius: 4px;
}

.overview-body {
  flex: 1;
  overflow: auto;
  padding: 0;
}

.art-overview,
.table-overview {
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.placeholder-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  color: #999;

  ::v-deep .el-empty {
    .el-empty__description {
      color: #999;
      font-size: 14px;
    }
  }
}

// 右侧详情内容样式
.detail-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 14px 20px;
  border-bottom: 1px solid #e8e8e8;
  background-color: #fafafa;
  flex-shrink: 0;
}

.detail-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  padding: 4px 8px;
  color: #666;

  &:hover {
    color: #409eff;
    background-color: rgba(64, 158, 255, 0.1);
  }
}

.detail-body {
  flex: 1;
  overflow: auto;
  padding: 0;
}

// 详情预留样式
.detail-placeholder {
  padding: 20px;

  ::v-deep .el-alert {
    .el-alert__content {
      .el-alert__description {
        p {
          margin: 8px 0;
          line-height: 1.5;

          strong {
            color: #409eff;
          }
        }
      }
    }
  }
}

// 表格资源详情样式
.detail-content-body {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  border-left: 4px solid #409eff;
  padding-left: 12px;
}

.check-result-content {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.result-text {
  margin: 0;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.6;
  color: #333;
  white-space: pre-wrap;
  word-wrap: break-word;
}

// 响应式设计
@media (max-width: 1200px) {
  .reports-layout {
    flex-direction: column;
    gap: 15px;
  }

  .reports-left,
  .reports-right {
    width: 100%;
  }

  .reports-left {
    height: 50%;
  }

  .reports-right {
    height: 50%;
  }
}

@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }

  .reports-header {
    padding: 12px 15px;
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .reports-filters {
    padding: 10px 15px;
    flex-direction: column;
    align-items: flex-start;
  }

  .reports-pagination {
    padding: 10px 15px;
  }
}
</style>
