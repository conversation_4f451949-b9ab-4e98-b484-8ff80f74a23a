<template>
  <div
    v-if="errorChartData.length > 0"
    :class="className"
    :style="{ height: height, width: width }"
  ></div>
  <el-empty v-else :image-size="200"></el-empty>
</template>

<script>
import echarts from "echarts";
require("echarts/theme/macarons"); // echarts theme
import resize from "./mixins/resize";

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: "chart",
    },
    width: {
      type: String,
      default: "100%",
    },
    height: {
      type: String,
      default: "650px",
    },
    autoResize: {
      type: Boolean,
      default: true,
    },
    // chartData: {
    //   type: Object,
    //   required: true,
    // },
    xAxisData: {
      type: Array,
      required: true,
    },
    errorChartData: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      chart: null,
      legendData: [],
      seriesData: [],
      selected: {},
      gridTop: "",
    };
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val);
      },
    },
    errorChartData: {
      deep: true,
      handler(val) {
        this.handleErrorChartData();
        this.$nextTick(() => {
          this.initChart();
        });
      },
    },
  },
  mounted() {
    // this.$nextTick(() => {
    //   this.initChart();
    // });
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    // 数组转对象方法
    arrTransferObj(e, key, val) {
      // 数组的reduce方法，使数组的obj初始值为{}，将数组中每一个对象所需的值，分别作为对象中的键与值
      return e.reduce((obj, item) => ((obj[item[key]] = item[val]), obj), {});
    },
    // echart 修改grid.top值
    getGridTop(legendData) {
      const legendCount = legendData.length;

      if (legendCount === 0) {
        return '70px';
      }

      // 根据图例数量动态计算top值
      let topValue;

      if (legendCount <= 6) {
        // 单行图例
        topValue = 70;
      } else if (legendCount <= 12) {
        // 双行图例
        topValue = 95;
      } else if (legendCount <= 18) {
        // 三行图例
        topValue = 120;
      } else {
        // 使用滚动图例，固定高度
        topValue = 110;
      }

      return topValue + 'px';
    },
    // 处理传来的数据
    handleErrorChartData() {
      // 清空之前的数据
      this.legendData = [];
      this.seriesData = [];

      // 保存legend
      let selectGroup = [];
      this.errorChartData.forEach((item, index) => {
        if (index < 1) {
          selectGroup.push({ type: item.type, isSelecte: true });
        } else {
          selectGroup.push({ type: item.type, isSelecte: false });
        }

        // 添加图例数据
        this.legendData.push(item.type);

        // 每个type的曲线
        this.seriesData.push({
          name: item.type,
          smooth: true,
          type: "line",
          data: item.count,
          animationDuration: 2800,
          animationEasing: "cubicInOut",
          lineStyle: {
            width: 2
          },
          symbol: 'circle',
          symbolSize: 4
        });
      });

      // 计算grid的top值
      this.gridTop = this.getGridTop(this.legendData);

      // 保存默认选中的数据
      this.selected = this.arrTransferObj(selectGroup, "type", "isSelecte");

      this.$nextTick(() => {
        this.initChart();
      });
    },
    initChart() {
      this.chart = echarts.init(this.$el, "macarons");
      // this.setOptions(this.chartData);
      this.setOptions();
    },
    // setOptions({ sameresource, mesh, texture, other } = {}) {
    setOptions() {
      this.chart.setOption({
        xAxis: {
          data: this.xAxisData,
          boundaryGap: false,
          axisTick: {
            show: false,
          },
          axisLabel: {
            interval: 0,
            rotate: 75,
            // textStyle: {
            //   fontSize: "12",
            // },
          },
        },
        grid: {
          left: 15,
          right: 10,
          bottom: 0,
          top: this.gridTop,
          containLabel: true,
        },
        tooltip: {
          trigger: "axis",
          formatter: function (params) {
            // 提示内容太多隔行显示内容
            let astr = "";
            params.forEach((ele, index) => {
              astr += `
                       <div style="display: block;height:20px;${
                         index % 2 === 0 ? "width: 50%;" : "width: 50%;"
                       }float:left;">
                        <i style="width: 10px;height: 10px;display: inline-block;background: ${
                          ele.color
                        };border-radius: 10px;"></i>
                            <span>${ele.seriesName}: ${ele.data}</span>
                        </div>
                        `;
            });
            const b = '<div style="width: 600px;">' + astr + "<div>";
            return b;
          },
          position: function (point, params, dom, rect, size) {
            return [point[1], 0];
          },
        },
        yAxis: {
          axisTick: {
            show: false,
          },
        },
        legend: {
          data: this.legendData,
          selected: this.selected,
          top: 15,
          left: 'center',
          orient: 'horizontal',
          itemGap: 12, // 图例项之间的间距
          itemWidth: 12, // 图例标记的图形宽度
          itemHeight: 12, // 图例标记的图形高度
          textStyle: {
            fontSize: 11,
            color: '#666'
          },
          // 当图例过多时使用滚动模式
          type: this.legendData.length > 8 ? 'scroll' : 'plain',
          pageButtonItemGap: 5,
          pageButtonGap: 20,
          pageIconColor: '#409eff',
          pageIconInactiveColor: '#ccc',
          pageIconSize: 12,
          pageTextStyle: {
            color: '#666',
            fontSize: 11
          },
          // 设置图例的最大宽度，超出时换行
          width: '90%',
          // 图例的高度，用于控制换行
          height: this.legendData.length > 12 ? 60 : (this.legendData.length > 6 ? 40 : 25)
        },
        series: this.seriesData,
      });
    },
  },
};
</script>
